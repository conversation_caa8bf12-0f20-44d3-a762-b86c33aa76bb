import {
  AfterViewChecked,
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  HostListener,
  Inject,
  OnD<PERSON>roy,
  OnInit,
  PLATFORM_ID,
  Renderer2,
  ViewChild,
} from '@angular/core';

import { DOCUMENT } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';

import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { SettingCategory } from '@components/lazy/app-setting/interfaces/setting-interfaces';
import { ChatBoxBubbleComponent } from '@components/lazy/chat-box/chat-box-bubble/chat-box-bubble.component';
import { FadeInDirective } from '@directives/fade-in.directive';
import { Chapter, ChapterPage, Comic, IServiceResponse, Page, UserExpType } from '@schema';
import { ComicService } from '@services/comic.service';
import { HistoryService } from '@services/history.service';
import { PopupService } from '@services/popup.service';
import { SeoService } from '@services/seo.service';
import { SettingService } from '@services/setting.service';
import { StorageService } from '@services/storage.service';
import { UrlService } from '@services/url.service';
import { ServiceUtility } from '@services/utils.service';
import { ChapterServer } from 'src/app/dataSource/schema/ChapterServer';
import { throttle } from 'src/app/utils';
export enum StickyState {
  NoneSticky = 0,
  StickyTop = 1,
  StickyBottom = 2,
  StickyInvisible = 3,
}
@Component({
  selector: 'app-chapter',
  templateUrl: './chapter-page.component.html',
  styleUrl: './chapter-page.component.scss',
  standalone: false,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ChapterPageComponent extends OptimizedBaseComponent
  implements AfterViewInit, AfterViewChecked, OnDestroy, OnInit {

  readonly bannerImg: Page = { url: '/banner-manga.png', pageNumber: 0 };
  listImgs?: Page[];
  listChapterServerIds: number[] = [];
  comic!: Comic;
  mainChapter!: ChapterPage;
  @ViewChild('screenContainer') screenContainer!: ElementRef;
  @ViewChild('readingContainer') readingContainer!: ElementRef;
  @ViewChild('imageContainer') imageContainer!: ElementRef;
  @ViewChild('controlBar') controlBar!: ElementRef;
  @ViewChild('MenuNavigation')
  MenuNavigation!: ElementRef;
  @ViewChild('commentComponent')
  commentComponent!: ElementRef;
  @ViewChild('HeaderContainer')
  HeaderContainer!: ElementRef;
  @ViewChild('EndChapter')
  endChapterElement!: ElementRef;

  @ViewChild(FadeInDirective) fadeInDirective?: FadeInDirective;


  stickyState: StickyState = StickyState.NoneSticky;
  lastScrollTop = 0;
  istotaly = false;
  toolbarOriginalPosition = 0;
  showScrollToTop = false;
  isImageLoading = true;
  selectedServerId: number = 0;
  showAllServers = false;
  startTickTop = 0;

  chapterSetting = {
    isFullScreen: false,
    isNightMode: false,
    isAutoNextChapter: false,
    isVertical: true,
    preloadPages: 5,
    fixedToolbar: false,
  };

  zoomData = {
    minZoomLevel: 50,
    maxZoomLevel: 150,
    zoomValue: 100,
    defaultZoomLevel: 100,
    isZoomIn: false,
    defaultWidth: 0,
  };

  constructor(
    private comicService: ComicService,
    private route: ActivatedRoute,
    private router: Router,
    private historyService: HistoryService,
    private seoService: SeoService,
    private renderer: Renderer2,
    private storage: StorageService,
    protected override cd: ChangeDetectorRef,
    private popupService: PopupService,
    private urlService: UrlService,
    private settingService: SettingService,
    @Inject(DOCUMENT) private document: Document,
    @Inject(PLATFORM_ID) protected override platformId: object,
  ) {
    super(cd, platformId);
  }

  ngAfterViewInit(): void {
    this.runInBrowser(() => {
      if (this.controlBar && this.toolbarOriginalPosition === 0) {
        this.toolbarOriginalPosition = this.controlBar.nativeElement.getBoundingClientRect().y;

      }
    })
  }
  ngAfterViewChecked(): void {

  }
  override ngOnDestroy(): void {

    ChatBoxBubbleComponent.Instance?.SetVisible(true);
  }

  getImageWidth() {

    return this.zoomData.zoomValue + '%'
  }
  getImageLeft() {
    return (100 - this.zoomData.zoomValue) * 0.5 + '%';
  }
  verifyZoom(value: number) {
    if (value <= 0) return 1;
    value = Math.min(value, this.zoomData.maxZoomLevel);
    value = Math.max(value, this.zoomData.minZoomLevel);
    return value;
  }

  loadSetting() {
    this.chapterSetting.isFullScreen = this.settingService.getSettingValue('doubleClickToFullscreen') ?? false;
    this.chapterSetting.isNightMode = this.settingService.getSettingValue('nightMode') ?? false;
    this.chapterSetting.isAutoNextChapter = this.settingService.getSettingValue('autoNextChapter') ?? false;
    this.chapterSetting.isVertical = this.settingService.getSettingValue('verticalReading') ?? true;
    this.chapterSetting.preloadPages = this.settingService.getSettingValue('preloadPages') ?? 3;
    this.zoomData.zoomValue = this.verifyZoom(this.settingService.getSettingValue('zoom-reading') ?? 100);
    this.chapterSetting.fixedToolbar = this.settingService.getSettingValue('fixedToolbar') ?? false;

    this.settingService.settingChanges$.subscribe((event) => {
      switch (event.settingId) {
        case 'doubleClickToFullscreen':
          this.chapterSetting.isFullScreen = event.newValue;
          break;
        case 'nightMode':
          this.chapterSetting.isNightMode = event.newValue;
          break;
        case 'autoNextChapter':
          this.chapterSetting.isAutoNextChapter = event.newValue;
          break;
        case 'verticalReading':
          this.chapterSetting.isVertical = event.newValue;
          this.changeDirectionReading(event.newValue);
          break;
        case 'zoom-reading':
          console.log(event.newValue);

          this.zoomData.zoomValue = event.newValue;
          break;
        case 'preloadPages':
          this.chapterSetting.preloadPages = event.newValue;
          break;
        case 'fixedToolbar':
          this.chapterSetting.fixedToolbar = event.newValue;
          break;
      }

      // console.log(event);
    });
  }

  ngOnInit(): void {
    this.runInBrowser(() => {
      ChatBoxBubbleComponent.Instance?.SetVisible(false);
      this.loadSetting();
    })
    this.loadChapterPage();

  }

  loadChapterPage() {
    this.isImageLoading = true;
    this.route.data.subscribe(({ ChapterImgRes }) => {
      this.toolbarOriginalPosition === 0
      const res = ChapterImgRes as IServiceResponse<ChapterPage>;
      if (res === null || res.data === null) {
        this.router.navigate(['/']);
        return;
      }
      const data = structuredClone(res.data)!;
      this.listChapterServerIds = [0, ...data.chapterServerIds];
      this.isImageLoading = false;
      this.comic = data.comic;
      this.mainChapter = data;
      this.listImgs = [this.bannerImg, ...this.mainChapter.pages];
      this.selectedServerId = 0;
      this.runInBrowser(() => {
        this.historyService.SaveHistory(this.comic, {
          id: this.mainChapter.id,
          title: this.mainChapter.title,
          slug: this.mainChapter.slug,
          updateAt: this.mainChapter.updateAt,
          viewCount: this.mainChapter.viewCount,
        });

        this.comicService
          .getChapters(this.comic.id)
          .subscribe((res: IServiceResponse<Chapter[]>) => {
            this.comic.chapters = res.data || [];
            this.safeMarkForCheck();
          });
      })

      this.SetupSeo();
      this.istotaly = false;
      this.safeMarkForCheck();
    });
  }



  changeServer(serverId: number, index: number) {
    this.selectedServerId = index;
    if (this.selectedServerId == 0) {
      this.listImgs = [this.bannerImg, ...this.mainChapter.pages];
      this.safeMarkForCheck();

      return
    }

    if (!serverId) {
      return;
    }

    this.comicService
      .getChapterServer(serverId)
      .subscribe((res: IServiceResponse<ChapterServer>) => {
        const pagesImages = res.data?.images ?? [];
        this.listImgs = [this.bannerImg, ...pagesImages.map((img) => {
          return {
            url: img,
            pageNumber: 0,
          };
        })];
        this.safeMarkForCheck();

      });
  }
  showMoreServer() {
    this.showAllServers = !this.showAllServers;
  }
  openSetting() {
    this.popupService.showSetting(SettingCategory.READING);
  }


  onChangeChapter(chapterId: number) {
    this.isImageLoading = true;
    this.router.navigate(['truyen-tranh', this.comic.url, chapterId]);
  }


  zoomImage(zoomIn: boolean): void {

    if (zoomIn) {
      this.zoomData.zoomValue = Math.min(
        this.zoomData.zoomValue + 10,
        this.zoomData.maxZoomLevel
      );
    } else {
      this.zoomData.zoomValue = Math.max(
        this.zoomData.zoomValue - 10,
        this.zoomData.minZoomLevel
      );
    }
    if (this.zoomData.zoomValue <= this.zoomData.minZoomLevel) {
      this.zoomData.isZoomIn = false;
      return;
    }
    if (this.zoomData.zoomValue >= this.zoomData.maxZoomLevel) {
      this.zoomData.isZoomIn = true;
      return;
    }
    this.settingService.setSettingValue('zoom-reading', this.zoomData.zoomValue);
  }

  resetView(): void {
    this.zoomData.isZoomIn = false;
    this.zoomData.zoomValue = this.zoomData.defaultZoomLevel;
    this.settingService.setSettingValue('zoom-reading', this.zoomData.zoomValue);
  }

  getZoomPercentage(): number {
    return Math.round((this.zoomData.zoomValue));
  }

  @HostListener('document:keydown.arrowleft', ['$event'])
  onNextChapter(): void {
    this.navigateChapter(true);
  }
  @HostListener('document:keydown.arrowright', ['$event'])
  onPreviousChapter(): void {
    this.navigateChapter(false);
  }

  navigateChapter = throttle((isNext: boolean): void => {
    if (this.isImageLoading) {
      return;
    }

    const container = this.imageContainer.nativeElement;
    const currentChapterIndex = this.comic.chapters!.findIndex(
      (chapter) => chapter.id === this.mainChapter.id
    );
    const targetChapterIndex = isNext
      ? currentChapterIndex + 1
      : currentChapterIndex - 1;
    // console.log(targetChapterIndex, this.comic.chapters!.length);

    if (
      targetChapterIndex >= 0 &&
      targetChapterIndex < this.comic.chapters!.length
    ) {
      const targetChapter = this.comic.chapters![targetChapterIndex];
      this.onChangeChapter(targetChapter.id);
      container.scrollLeft = 0;
      container.scrollTop = 0;
    }
  }, 1000);

  @HostListener('document:fullscreenchange', ['$event'])
  onFullscreenChange(event: Event) {
    this.chapterSetting.isFullScreen = document.fullscreenElement != null
    this.safeMarkForCheck();
  }
  widthInner = 0;

  toggleFullscreen(): void {
    const elem = this.screenContainer.nativeElement;
    if (!this.chapterSetting.isFullScreen) {
      if (elem.requestFullscreen) {
        elem.requestFullscreen();
      } else if (elem.mozRequestFullScreen) {
        // Firefox
        elem.mozRequestFullScreen();
      } else if (elem.webkitRequestFullscreen) {
        // Chrome, Safari, and Opera
        elem.webkitRequestFullscreen();
      } else if (elem.msRequestFullscreen) {
        // IE/Edge
        elem.msRequestFullscreen();
      }

      this.widthInner = this.imageContainer.nativeElement.clientWidth;

    }
    else {
      this.document.exitFullscreen();
    }
  }



  scrollToTop(event: Event): void {
    event.preventDefault();
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }



  onCheckboxChange(event: Event): void {
    const checkbox = event.target as HTMLInputElement;
    this.chapterSetting.isAutoNextChapter = checkbox.checked;
  }

  @HostListener('window:resize', ['$event'])
  onResize(_event: Event) {
    this.zoomData.defaultWidth = this.HeaderContainer.nativeElement.clientWidth;
    this.cd.markForCheck();
  }
  @HostListener('window:scroll', ['$event'])
  handleScroll() {
    if (!this.controlBar) return;
    const windowScroll = window.scrollY;
    const windowHeight = window.innerHeight;

    const documentHeight = document.documentElement.scrollHeight;

    const isLastPage = windowScroll + windowHeight >= documentHeight - 1;
    if (isLastPage) {
      if (this.chapterSetting.isAutoNextChapter && this.chapterSetting.isVertical) {
        this.navigateChapter(false);
        return;
      } else {
        this.showScrollToTop = true;
        return;
      }
    }
    // this.isStickyBottom = (windowScroll + windowHeight > this.endChapterElement.nativeElement.offsetTop)
    this.updateStickerState();

    this.lastScrollTop = windowScroll <= 0 ? 0 : windowScroll;

    const scrollOffset =
      window.scrollY ||
      document.documentElement.scrollTop ||
      document.body.scrollTop ||
      0;

    if (!this.istotaly && scrollOffset > 2000) {
      this.istotaly = true;
      this.comicService
        .updateViewAndExp(
          this.comic.id,
          this.mainChapter.id,
          UserExpType.Chapter
        )
        .subscribe(() => { });
    }
  }

  // Performance optimization methods
  trackByPageId = (index: number, page: Page): any => {
    return page?.url ?? index;
  };

  updateStickerState() {
    const windowScroll = window.scrollY;
    const windowHeight = window.innerHeight;
    const preStickyState = this.stickyState;


    const isEndChapter =
      windowScroll + windowHeight >
      this.endChapterElement.nativeElement.offsetTop;
    if (isEndChapter && !this.chapterSetting.fixedToolbar) {
      this.stickyState = StickyState.StickyBottom;
    }

    if (!isEndChapter) {
      if (windowScroll > this.toolbarOriginalPosition) {
        if (this.chapterSetting.fixedToolbar) {
          this.stickyState = StickyState.StickyTop;
        }
        else
          if (windowScroll < this.lastScrollTop) {
            this.stickyState = StickyState.StickyTop;
            this.showScrollToTop = true;
            this.startTickTop = windowScroll;
          }

          else {
            if (
              this.stickyState == StickyState.StickyTop &&
              windowScroll - this.startTickTop > 200
            ) {
              this.stickyState = StickyState.StickyInvisible;
              this.showScrollToTop = false;
            }
          }
      } else {
        this.stickyState = StickyState.NoneSticky;
      }
    }
    if (preStickyState != this.stickyState) {
      switch (this.stickyState as StickyState) {
        case StickyState.StickyBottom:
          this.stickToBottom();
          break;
        case StickyState.StickyTop:
          this.stickToTop();
          break;
        case StickyState.StickyInvisible:
          this.showSticky(false);
          break;
        case StickyState.NoneSticky:
          this.cancelSticky();
          break;
      }
    }
  }
  cancelSticky() {
    this.showSticky(true);
    this.controlBar.nativeElement.classList.remove(
      'sticky-bottom',
      'sticky-top'
    );
    this.renderer.addClass(this.controlBar.nativeElement, 'rounded-b-lg');
  }
  stickToTop() {
    this.showSticky(true);

    this.renderer.addClass(this.controlBar.nativeElement, 'sticky-top');
    this.renderer.removeClass(this.controlBar.nativeElement, 'rounded-b-lg');
    this.controlBar.nativeElement.classList.remove('sticky-bottom');
  }
  stickToBottom() {
    this.showSticky(true);
    this.renderer.addClass(this.controlBar.nativeElement, 'sticky-bottom');
    this.controlBar.nativeElement.classList.remove('sticky-top');
  }

  showSticky(isVisible: boolean) {
    if (isVisible) {
      this.fadeInDirective?.fadeIn();
    } else {
      this.fadeInDirective?.fadeOut();
    }
  }

  changeDirectionReading(stage: boolean) {
    this.chapterSetting.isVertical = stage;
    this.addMouseWheelEvent();
    const styles = this.chapterSetting.isVertical
      ? {
        'scroll-snap-align': 'start',
        flex: '0 0 auto',
        display: 'flex',
        'flex-direction': 'column',
        'overflow-y': 'auto',
        'overflow-x': 'hidden',
      }
      : {
        'margin-top': '30px',
        'min-width': '30rem',
        'scroll-snap-align': 'start',
        display: 'flex',
        'flex-direction': 'row',
        overflow: 'hidden',
        'overflow-x': 'auto',
        'overflow-y': 'hidden',
      };

    for (const [key, value] of Object.entries(styles)) {
      this.renderer.setStyle(this.imageContainer.nativeElement, key, value);
    }
  }

  enableNightLight(stage: boolean) {
    this.chapterSetting.isNightMode = stage;
  }

  addMouseWheelEvent() {
    const container = this.imageContainer.nativeElement;
    container.removeEventListener('wheel', this.handleWheelEvent);
    container.addEventListener('wheel', this.handleWheelEvent);
  }

  scrollHorizontal(direction: number) {
    const container = this.imageContainer.nativeElement;
    const scrollAmount = direction * 500;
    container.scrollBy({
      left: scrollAmount,
      behavior: 'smooth',
    });
    if (this.chapterSetting.isAutoNextChapter) {
      this.checkScrollEnd();
    }
  }

  handleWheelEvent = (event: WheelEvent) => {
    const container = this.imageContainer.nativeElement;
    const scrollGap = 2.5;
    if (!this.chapterSetting.isVertical) {
      container.scrollBy({
        left: event.deltaY * scrollGap,
        behavior: 'smooth',
      });
      if (this.chapterSetting.isAutoNextChapter) {
        this.checkScrollEnd();
      }
      event.preventDefault();
    }
  };

  checkScrollEnd() {
    const container = this.imageContainer.nativeElement;

    if (this.chapterSetting.isVertical) {
      if (
        container.scrollTop + container.clientHeight >=
        container.scrollHeight
      ) {
        this.navigateChapter(true);
        container.scrollTop = 0;
        container.scrollLeft = 0;
      }
    } else {
      if (
        container.scrollLeft + container.clientWidth >=
        container.scrollWidth
      ) {
        this.navigateChapter(false);
        container.scrollTop = 0;
        container.scrollLeft = 0;
      }
    }
  }
  SetupSeo() {
    const title = `${this.comic.title} Chapter ${this.mainChapter.slug} - MeTruyenMoi`;
    const description = `[Chapter ${this.mainChapter.slug
      }] - ${ServiceUtility.fillSeoDescription(this.comic.description, {
        title: this.comic.title,
      })} - MeTruyenMoi`;
    const url = `${this.urlService.BASE_URL}/truyen-tranh/${this.comic.url}/${this.mainChapter.id}`;
    this.seoService.setTitle(title);
    this.seoService.addTags([
      { name: 'description', content: description },
      {
        name: 'keywords',
        content: ` ${this.comic.title}, ${this.comic.url}, ${this.comic.title} chapter ${this.mainChapter.slug} ,Mê Truyện Mới, metruyenmoi`,
      },
      { property: 'og:description', content: description },
      { property: 'og:title', content: title },
      { property: 'og:url', content: url },
      { property: 'og:type', content: 'article' },
      { property: 'og:site_name', content: 'MeTruyenMoi' },
      { itemprop: 'name', content: this.comic.title },
      { itemprop: 'description', content: description },
    ]);
    this.seoService.updateLink('canonical', url);
  }

  reportError() {
    this.popupService.showReportComic({
      comicID: this.comic.id,
      chapterID: this.mainChapter.id,
    });
  }

  get TopToBottom() {
    return this.stickyState !== StickyState.StickyBottom;
  }
  onLoad($event: Event) {
    const imageEle = $event.target as HTMLImageElement;
    imageEle.onload = null;
    imageEle.classList.remove('min-h-48');
  }
  onError($event: ErrorEvent) {
    const imageEle = $event.target as HTMLImageElement;
    imageEle.onerror = null;
    imageEle.classList.add('hidden');
  }
}
