<!-- Modern Settings Modal - Comic Website Design -->
<div *ngIf="isVisible()" class="settings-overlay">
  <!-- Modal Container -->
  <div class="settings-modal">
    <!-- Header -->
    <div class="settings-header">
      <div class="settings-title-section">
        <div class="settings-icon">
          <svg
            class="size-6"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path stroke="none" d="M0 0h24v24H0z" />
            <path
              d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
            />
            <circle cx="12" cy="12" r="3" />
          </svg>
        </div>
        <div class="settings-title-text">
          <h2 class="settings-title">Cài đặt</h2>
          <p class="settings-subtitle">Tùy chỉnh trải nghiệm của bạn</p>
        </div>
      </div>

      <!-- Search Bar -->
      <div class="settings-search">
        <div class="search-input-wrapper">
          <svg
            class="search-icon"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
          >
            <circle cx="11" cy="11" r="8" />
            <path d="m21 21-4.35-4.35" />
          </svg>
          <input
            type="text"
            placeholder="Tìm kiếm cài đặt..."
            class="search-input"
            [value]="searchTerm()"
            (input)="onSearchChange($any($event.target).value)"
          />
        </div>
      </div>

      <!-- Close Button -->
      <button (click)="close()" class="settings-close-button" aria-label="Đóng cài đặt">
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="18" y1="6" x2="6" y2="18" />
          <line x1="6" y1="6" x2="18" y2="18" />
        </svg>
      </button>
    </div>

    <!-- Main Content -->
    <div class="settings-content">
      <!-- Tab Navigation -->
      <div class="settings-tabs">
        <button
          *ngFor="let tab of settingTabs(); trackBy: trackByTabId"
          (click)="selectTab(tab.category)"
          class="settings-tab"
          [class.active]="selectedTab() === tab.category"
        >
          <span class="tab-label">{{ tab.name }}</span>
        </button>
      </div>

      <!-- Settings Panel -->
      <div class="settings-panel">
        <ng-container *ngIf="currentGroup() as group">
          <!-- Group Header -->
          <div class="settings-group-header">
            <h3 class="group-title">{{ group.name }}</h3>
            <p class="group-description">{{ group.description }}</p>
            <button (click)="resetToDefaults()" class="reset-button" title="Khôi phục mặc định">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8" />
                <path d="M21 3v5h-5" />
                <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16" />
                <path d="M3 21v-5h5" />
              </svg>
              Khôi phục
            </button>
          </div>

          <!-- Settings List -->
          <div class="settings-list">
            <div
              *ngFor="let setting of group.settings; trackBy: trackBySettingId"
              class="setting-item"
            >
              <div class="setting-info">
                <label class="setting-label" [for]="setting.id">
                  {{ setting.name }}
                </label>
                <p class="setting-description">{{ setting.description }}</p>
              </div>

              <div class="setting-control">
                <!-- Toggle Input -->
                <app-setting-toggle
                  *ngIf="getInputType(setting) === 'toggle'"
                  [setting]="setting"
                  [value]="settingService.getSettingValue(setting.id)"
                  (onChange)="onSettingChange(setting, $event)"
                />

                <!-- Range Input -->
                <app-setting-range
                  *ngIf="getInputType(setting) === 'range'"
                  [setting]="setting"
                  [value]="settingService.getSettingValue(setting.id)"
                  (onChange)="onSettingChange(setting, $event)"
                />

                <!-- Selection Input -->
                <app-selection-2
                  *ngIf="getInputType(setting) === 'selection'"
                  [options]="setting.options || []"
                  [selectedValue]="settingService.getSettingValue(setting.id)"
                  (selectedValueChange)="onSettingChange(setting, $event)"
                  class="setting-selection"
                />

                <!-- Color Input -->
                <div *ngIf="getInputType(setting) === 'color'" class="setting-color-wrapper">
                  <input
                    type="color"
                    [id]="setting.id"
                    [value]="settingService.getSettingValue(setting.id)"
                    (change)="onSettingChange(setting, $any($event.target).value)"
                    class="setting-color-input"
                  />
                  <span class="setting-color-value">
                    {{ settingService.getSettingValue(setting.id) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </ng-container>

        <!-- Empty State -->
        <div *ngIf="!currentGroup()" class="settings-empty">
          <div class="empty-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="11" cy="11" r="8" />
              <path d="m21 21-4.35-4.35" />
            </svg>
          </div>
          <h3 class="empty-title">Không tìm thấy cài đặt</h3>
          <p class="empty-description">Thử tìm kiếm với từ khóa khác hoặc chọn danh mục khác.</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Backdrop -->
  <div (click)="close()" class="settings-backdrop"></div>
</div>
