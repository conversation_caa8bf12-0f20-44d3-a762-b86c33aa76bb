// ===== MODERN COMIC DETAIL POPUP STYLES =====
// Beautiful, responsive design with comic website aesthetics

// ===== MAIN CONTAINER =====
.popup-detail-container {
  @apply relative w-[32rem] max-w-[90vw] flex flex-col overflow-hidden;
  @apply bg-white dark:bg-neutral-900 rounded-2xl shadow-2xl;
  @apply border border-neutral-200 dark:border-neutral-700;

  // Modern backdrop blur effect
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);

  .dark & {
    background: rgba(23, 23, 23, 0.95);
  }

  // Smooth entrance animation
  animation: slideInUp 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

// ===== BACKGROUND GRADIENT =====
.popup-background {
  @apply absolute inset-0 opacity-30 pointer-events-none;
  background: linear-gradient(135deg,
    theme('colors.primary.100') 0%,
    theme('colors.primary.200') 50%,
    theme('colors.primary.300') 100%
  );
  border-radius: inherit;
}

// ===== MAIN CONTENT =====
.popup-content {
  @apply relative z-10 p-6 flex flex-col gap-5;
}

// ===== HEADER SECTION =====
.popup-header {
  @apply flex flex-col gap-3;
}

.title-section {
  @apply flex items-start justify-between gap-3;
}

.comic-title {
  @apply text-xl font-bold text-neutral-900 dark:text-white leading-tight;
  @apply hover:text-primary-100 transition-colors duration-200;
  flex: 1;

  // Gradient text effect on hover
  &:hover {
    background: linear-gradient(45deg, theme('colors.primary.100'), theme('colors.primary.200'));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

// ===== STATUS BADGE =====
.status-badge {
  @apply flex-shrink-0;
}

.status-indicator {
  @apply flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium;
  @apply transition-all duration-200 hover:scale-105;

  &.ongoing {
    @apply bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300;
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  &.completed {
    @apply bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300;
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
  }
}

.status-dot {
  @apply w-2 h-2 rounded-full bg-current animate-pulse;
}

.status-icon {
  @apply w-4 h-4 animate-pulse;
}

.status-text {
  @apply font-semibold;
}

// ===== AUTHOR SECTION =====
.author-section {
  @apply flex items-center gap-2 text-neutral-600 dark:text-neutral-400;
  @apply hover:text-primary-100 transition-colors duration-200;
}

.author-icon {
  @apply w-4 h-4 stroke-current;
}

.author-name {
  @apply text-sm font-medium;
}

// ===== GENRES SECTION =====
.genres-section {
  @apply -mx-1;
}

.genres-container {
  @apply flex flex-wrap gap-2;
}

.genre-tag {
  @apply px-3 py-1 text-xs font-semibold rounded-full;
  @apply bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300;
  @apply hover:bg-neutral-200 dark:hover:bg-neutral-700;
  @apply transition-all duration-200 hover:scale-105 cursor-pointer;

  &.primary-genre {
    @apply bg-gradient-to-r from-primary-100 to-primary-200 text-white;
    @apply hover:from-primary-200 hover:to-primary-300;
    box-shadow: 0 4px 15px rgba(248, 110, 76, 0.3);
  }
}

// ===== STATS SECTION =====
.stats-section {
  @apply grid grid-cols-3 gap-4;
}

.stat-item {
  @apply flex flex-col items-center gap-2 p-3 rounded-xl;
  @apply bg-neutral-50 dark:bg-neutral-800/50 border border-neutral-200 dark:border-neutral-700;
  @apply hover:bg-neutral-100 dark:hover:bg-neutral-800;
  @apply transition-all duration-200 hover:scale-105 hover:shadow-lg;

  &.rating {
    @apply hover:border-yellow-300 dark:hover:border-yellow-600;

    .stat-icon {
      @apply text-yellow-500;
    }
  }

  &.views {
    @apply hover:border-blue-300 dark:hover:border-blue-600;

    .stat-icon {
      @apply text-blue-500;
    }
  }

  &.chapters {
    @apply hover:border-green-300 dark:hover:border-green-600;

    .stat-icon {
      @apply text-green-500;
    }
  }
}

.stat-icon {
  @apply w-5 h-5 transition-transform duration-200;

  .stat-item:hover & {
    @apply scale-110;
  }
}

.stat-value {
  @apply text-lg font-bold text-neutral-900 dark:text-white;
}

.stat-label {
  @apply text-xs text-neutral-600 dark:text-neutral-400 font-medium;
}