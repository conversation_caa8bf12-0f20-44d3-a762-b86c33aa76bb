import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnInit, PLATFORM_ID } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { Comic, ComicStatus, Genre, SortType } from '@schema';
import { ComicService } from '@services/comic.service';
import { SeoService } from '@services/seo.service';
import { UrlService } from '@services/url.service';
import {
  IFilters,
  genreFiltersOptions,
} from '../../components/utils/constants';
import { GenreService } from '@services/genre.service';

@Component({
  selector: '[app-genre]',
  templateUrl: './genre.component.html',
  styleUrl: './genre.component.scss',
  standalone: false,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GenreComponent extends OptimizedBaseComponent implements OnInit {
  listComics: Comic[] = [];
  currentGenre?: Genre;

  totalpage!: number;
  totalResult!: number;
  currentPage = 1;
  dataView!: IFilters;

  selectedComic?: Comic;
  isShowDetails = false;

  selectOptions: any = {
    sorts: { value: SortType.LastUpdate, name: 'Mới cập nhật' },
    status: { value: ComicStatus.ALL, name: 'Tất cả' },
  };

  private genreSlug = '';
  isLoading = false;

  constructor(
    private comicService: ComicService,
    private route: ActivatedRoute,
    private router: Router,
    private seoService: SeoService,
    private urlService: UrlService,
    override cd: ChangeDetectorRef,
    private genreService: GenreService,
    @Inject(PLATFORM_ID) override platformId: object,
  ) {
    super(cd, platformId);
    this.dataView = {
      status: genreFiltersOptions.status,
      sorts: genreFiltersOptions.sorts,
    };
  }

  ngOnInit(): void {
    // Subscribe to route params to get genre slug
    this.route.params.pipe(this.takeUntilDestroy()).subscribe((params: any) => {
      this.genreSlug = params['slug'];
      console.log(this.genreSlug)
      this.loadGenreInfo();
    });

    // Subscribe to query params for pagination and filters
    this.route.queryParams.pipe(this.takeUntilDestroy()).subscribe((params: any) => {
      const page = Number(params['page']) || 1;
      const status = Number(params['status']) >= 0 ? Number(params['status']) : ComicStatus.ALL;
      const sort = Number(params['sort']) >= 0 ? Number(params['sort']) : SortType.LastUpdate;

      this.currentPage = page;
      this.selectOptions.sorts.value = sort;
      this.selectOptions.status.value = status;

      if (this.currentGenre) {
        this.loadComicsByGenre(page, sort, status);
      }
    });
  }

  /**
   * Load genre information
   */
  private loadGenreInfo(): void {
    if (!this.genreSlug) return;
    this.genreService.getGenreBySlug(this.genreSlug)
      .pipe(this.takeUntilDestroy())
      .subscribe((genre: any) => {
        if (!genre) {
          this.router.navigate(['/404']);
          return;
        }
        this.currentGenre = genre
        this.setupSeo();

        // Load comics after genre info is loaded
        const page = this.currentPage;
        const sort = this.selectOptions.sorts.value;
        const status = this.selectOptions.status.value;
        this.loadComicsByGenre(page, sort, status);

        this.safeMarkForCheck();
      });
  }

  /**
   * Load comics by genre with filters
   */
  private loadComicsByGenre(page: number, sort: number, status: number): void {
    if (!this.currentGenre || this.isLoading) return;

    this.isLoading = true;
    this.comicService
      .getComics({
        step: '35',
        genre: this.currentGenre.id.toString(),
        page: page.toString(),
        sort: sort.toString(),
        status: status.toString(),
      })
      .pipe(this.takeUntilDestroy())
      .subscribe((res: any) => {
        this.totalpage = res.data.totalpage;
        this.totalResult = res.data.totalResult || 0;
        this.listComics = res.data.comics || [];

        // Show details of the first comic if not already selected
        if (!this.selectedComic && this.listComics.length > 0) {
          this.showDetails(this.listComics[0]);
        }

        this.isLoading = false;
        this.safeMarkForCheck();
      });
  }

  /**
   * Handle sort option change
   */
  onSortOptionChange(value: number): void {
    this.selectOptions.sorts.value = value;
    this.router.navigate([], {
      queryParams: {
        sort: value,
        page: 1 // Reset to first page when changing sort
      },
      queryParamsHandling: 'merge',
      fragment: 'listComic',
    });
  }

  /**
   * Handle status option change
   */
  onStatusOptionChange(value: number): void {
    this.selectOptions.status.value = value;
    this.router.navigate([], {
      queryParams: {
        status: value,
        page: 1 // Reset to first page when changing status
      },
      queryParamsHandling: 'merge',
      fragment: 'listComic',
    });
  }

  /**
   * Show comic details
   */
  showDetails(comic: Comic): void {
    this.selectedComic = comic;
  }

  /**
   * Handle page change
   */
  onChangePage(page: number): void {
    this.router.navigate([], {
      queryParams: { page: page },
      queryParamsHandling: 'merge',
      fragment: 'listComic',
    });
  }

  /**
   * Setup SEO for genre page
   */
  private setupSeo(): void {
    if (!this.currentGenre) return;

    const title = `Truyện tranh ${this.currentGenre.title}`;
    const description = this.currentGenre.description ||
      `Đọc truyện tranh thể loại ${this.currentGenre.title} online miễn phí. Kho tàng truyện ${this.currentGenre.title} phong phú, cập nhật liên tục tại MeTruyenMoi.`;

    // Use SeoService to set comprehensive SEO
    this.seoService.setGenreSEO(this.currentGenre, this.listComics, this.currentPage);
  }

  /**
   * Get genre description for display
   */
  get genreDescription(): string {
    if (!this.currentGenre) return '';

    return this.currentGenre.description ||
      `Khám phá kho tàng truyện tranh thể loại ${this.currentGenre.title} với những câu chuyện hấp dẫn, đa dạng và phong phú. Đọc online miễn phí tại MeTruyenMoi.`;
  }

  /**
   * Get current filter summary
   */
  /**
   * Track by function for comic list optimization
   */
  trackByComicId = (index: number, comic: Comic): number => comic.id;
}
