using ComicAPI.Models;
namespace ComicAPI.DTOs;

public class ChapterServerDTO
{
    public int ID { get; set; }
    // public string ServerName { get; set; } = null!; 
    // public string? Host { get; set; }
    // public string? Referer { get; set; }
    public string[]? Images { get; set; }
    // public string? Code { get; set; }
    // public int Status { get; set; }
    // public int IsDefault { get; set; }

    public ChapterServerDTO(ChapterServer chapterServer)
    {
        ID = chapterServer.ID;
        // ServerName = chapterServer.ServerName ?? string.Empty;
        // Host = chapterServer.Host;
        // Referer = chapterServer.Referer;
        Images = chapterServer.Images;
        // Code = chapterServer.Code;
        // Status = chapterServer.Status;
        // IsDefault = chapterServer.IsDefault;
    }
}
