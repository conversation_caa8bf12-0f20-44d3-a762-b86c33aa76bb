// ===== CHAPTER PAGE COMPONENT STYLES =====
// Modern, clean design with Tailwind CSS @apply directives
.chapter-container
{
  @apply relative bg-[#333] dark:bg-dark-bg flex flex-col overflow-y-auto overflow-x-hidden;
}

// ===== BASE LAYOUT =====

.header-container {
  @apply  text-white mx-auto md:container w-full mb-3;
}

.breadcrumb-wrapper {
  @apply z-10 my-2 mx-auto flex;
}

.main-container {
  @apply min-h-screen mx-auto md:container w-full;
}

// ===== CHAPTER HEADER =====
.chapter-header-container {
  @apply relative mx-auto flex flex-col items-center font-bold text-base w-full z-10;
}

.chapter-header-card {
  @apply bg-white dark:bg-neutral-800 border text-black dark:text-white p-4 lg:p-6 w-full rounded-t-xl dark:border-neutral-700 z-20;
}

.chapter-info-section {
  @apply text-center space-y-4;
}

// ===== REPORT ERROR BUTTON =====
.report-error-button {
  @apply flex items-center gap-2 px-3 py-1.5 bg-yellow-50 dark:bg-yellow-900/20 hover:bg-yellow-100 dark:hover:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 rounded-lg border border-yellow-200 dark:border-yellow-800 hover:border-yellow-300 dark:hover:border-yellow-700;
}

.report-icon {
  @apply w-5 h-5;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.report-text {
  @apply text-xs font-bold;
}

// ===== COMIC TITLE =====
.comic-title-section {
  @apply space-y-2;
}

.comic-title {
  @apply text-center;
}

.comic-title-link {
  @apply text-xl lg:text-2xl font-bold uppercase text-primary-100 hover:text-primary-200 hover:underline transition-colors duration-200 cursor-pointer;
}

// ===== CHAPTER DETAILS =====
.chapter-details {
  @apply space-y-3;
}

.chapter-title {
  @apply text-base lg:text-lg font-semibold text-gray-900 dark:text-white;
}

.chapter-date {
  @apply text-xs font-medium text-gray-500 dark:text-gray-300;
}

// ===== SERVER SELECTION =====
.server-selection-section {
  @apply mt-4;
}

.server-list {
  @apply flex flex-wrap items-center justify-center gap-3;
}

.server-button {
  @apply flex items-center gap-2 px-3 py-1.5 bg-gray-100 dark:bg-neutral-700 hover:bg-gray-200 dark:hover:bg-neutral-600 text-gray-600 dark:text-gray-300 rounded-lg border border-gray-200 dark:border-neutral-600 text-sm font-medium;

  &.server-button-active {
    @apply text-sky-500  border-sky-500;
  }
}

.server-icon {
  @apply w-5 h-5;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.server-text {
  @apply font-medium;
}

.server-expand-button {
  @apply flex items-center justify-center p-2 bg-gray-100 dark:bg-neutral-700 hover:bg-gray-200 dark:hover:bg-neutral-600 text-gray-600 dark:text-gray-300 rounded-lg border border-gray-200 dark:border-neutral-600;
}

.expand-icon {
  @apply w-5 h-5 transition-transform duration-200;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;

  &.expand-icon-rotated {
    @apply rotate-180;
  }
}
// ===== UTILITY CLASSES =====



// ===== CONTROL BAR =====
.control-bar {
  @apply flex  justify-center w-full items-center z-10 rounded-b-lg py-1.5 gap-2 md:gap-3;
  @apply bg-white dark:bg-neutral-800 dark:border-neutral-700;
  @apply transition-all duration-500 ease-in-out z-[999];
  @apply transform-gpu; // Enable hardware acceleration

  // Default state - visible
  // transform: translateY(0);
  // opacity: 1;

  &.sticky-top {
  @apply fixed right-0 top-0 rounded-b-none;
}
  &.sticky-invisible {
  @apply fixed right-0 -top-12;
}

  // Animation states
  // &.control-bar-hidden {
  //   transform: translateY(-100%);
  //   opacity: 0;
  //   pointer-events: none;
  // }
  // &.control-bar-visible {
  //   transform: translateY(0);
  //   opacity: 1;
  //   pointer-events: auto;
  // }

  // Slide down animation for showing
  // &.control-bar-slide-down {
  //   animation: slideDown 0.3s ease-out forwards;
  // }

  // // Slide up animation for hiding
  // &.control-bar-slide-up {
  //   animation: slideUp 0.3s ease-in forwards;
  // }

  // // Sticky state styling
  // &.control-bar-sticky {
  //   @apply shadow-lg backdrop-blur-sm;
  //   border-bottom: 1px solid rgba(248, 110, 76, 0.2);
  // }
}



.control-group {
  @apply z-10 flex items-center gap-2;
}

.control-button {
  @apply flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-primary-100 hover:bg-gray-100 dark:hover:bg-neutral-700 rounded-lg border border-gray-200 dark:border-neutral-600 hover:border-primary-100/50;

  &.control-button-home {
    @apply text-primary-100 border-primary-100/30 bg-primary-100/5;
  }

  &.settings-button {
    @apply bg-gray-50 dark:bg-neutral-700;
  }
}

.control-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

// ===== CHAPTER NAVIGATION =====
.chapter-navigation-group {
  @apply flex items-center gap-1 px-4;
}

.nav-button {
  @apply flex items-center gap-2 px-2 py-2 text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-gray-200 dark:bg-neutral-600 hover:bg-primary-100 rounded-lg border-none cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed;

  &.nav-button-active {
    @apply text-white bg-primary-100;
  }
}

.nav-icon {
  @apply w-5 h-5;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.chapter-selector-wrapper {
  @apply mx-1;
}

// ===== ZOOM CONTROLS =====
.zoom-group {
  @apply relative;
}

.zoom-button {
  @apply relative;
}

.zoom-panel {
  @apply absolute hidden bg-white dark:bg-neutral-800 rounded-lg shadow-lg border border-gray-200 dark:border-neutral-700 top-12 p-4 space-y-3 items-center -translate-x-full min-w-40 z-[9999];

  &.zoom-panel-active {
    @apply block;
  }
}

.zoom-info {
  @apply flex items-center gap-4 w-full;
}

.zoom-percentage {
  @apply text-primary-100 text-sm font-semibold min-w-12;
}

.zoom-controls {
  @apply flex gap-2;
}

.zoom-control-btn {
  @apply flex items-center justify-center w-8 h-8 text-gray-600 dark:text-gray-300 hover:text-primary-100 hover:bg-gray-100 dark:hover:bg-neutral-700 rounded-md transition-all duration-200;
}

.zoom-control-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.zoom-reset-btn {
  @apply flex items-center gap-2 px-3 py-1.5 bg-primary-100 hover:bg-primary-200 text-white text-sm font-medium rounded-lg border-none cursor-pointer;
}

.zoom-reset-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

// ===== SETTINGS MENU =====
.settings-menu {
  @apply absolute -z-50 bottom-0 transition-transform px-4 py-4 w-full bg-white dark:bg-neutral-800 shadow-lg border-t border-gray-200 dark:border-neutral-700;
}

.settings-menu-content {
  @apply -z-50 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 items-start justify-center lg:w-4/5 xl:w-3/4 mx-auto;
}

.settings-option {
  @apply space-y-3;
}

.settings-option-title {
  @apply text-sm font-semibold text-gray-900 dark:text-white mb-2;
}

.settings-option-buttons {
  @apply flex flex-col gap-2;
}

.settings-btn {
  @apply flex items-center gap-3 px-4 py-3 text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white bg-gray-50 dark:bg-neutral-700 hover:bg-gray-100 dark:hover:bg-neutral-600 rounded-lg border border-gray-200 dark:border-neutral-600 cursor-pointer;

  &.settings-btn-active {
    @apply text-primary-100 bg-primary-100/10 border-primary-100/30;
  }

  &.settings-btn-full {
    @apply w-full justify-center;
  }
}

.settings-btn-icon {
  @apply w-5 h-5 flex-shrink-0;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.settings-btn-text {
  @apply text-left;
}

// ===== TOGGLE SWITCH =====
.settings-toggle {
  @apply flex items-center gap-3 cursor-pointer;
}

.settings-toggle-input {
  @apply sr-only;
}

.settings-toggle-slider {
  @apply relative w-12 h-6 bg-gray-200 dark:bg-neutral-600 rounded-full transition-colors duration-200;

  .settings-toggle-input:checked+& {
    @apply bg-primary-100;
  }
}

.settings-toggle-thumb {
  @apply absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full shadow-sm transition-transform duration-200;

  .settings-toggle-input:checked+.settings-toggle-slider & {
    @apply translate-x-6;
  }
}

.settings-toggle-label {
  @apply text-sm font-medium text-gray-700 dark:text-gray-300;
}

// ===== BANNER SECTION =====
.banner-section {
  @apply my-2 gap-2 flex flex-col;
}

// ===== READING CONTAINER =====
.reading-container {
  @apply sm:px-[5%] md:px-[15%] relative z-0;
}

.reading-content {
  @apply  relative min-h-screen;
}

// ===== SCROLL NAVIGATION =====
.scroll-navigation {
  @apply absolute flex justify-between w-full z-20 h-1/4 top-1/3 px-4;

  &.scroll-navigation-hidden {
    @apply hidden;
  }
}

.scroll-btn {
  @apply flex items-center gap-2 px-4 py-3 bg-black/20 hover:bg-black/40 text-white rounded-lg backdrop-blur-sm border border-white/20;

  &.scroll-btn-prev {
    @apply bg-gradient-to-r from-black/30 to-transparent;
  }

  &.scroll-btn-next {
    @apply bg-gradient-to-l from-black/30 to-transparent;
  }
}

.scroll-btn-icon {
  @apply w-6 h-6;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.scroll-btn-text {
  @apply font-medium hidden sm:inline;
}

// ===== LOADING STATE =====
.loading-container {
  @apply flex flex-col items-center justify-center mt-10 space-y-8 p-8;
}

.loading-content {
  @apply flex flex-col items-center space-y-4;
}

.loading-spinner {
  @apply relative;
}

.loading-icon {
  @apply w-16 h-16 text-primary-100;
}

.loading-circle-bg {
  @apply opacity-25;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
}

.loading-circle-progress {
  @apply opacity-75;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  animation: loading-progress 2s ease-in-out infinite;
  stroke-dasharray: 31.416;
  stroke-dashoffset: 31.416;
}

@keyframes loading-progress {
  0% {
    stroke-dashoffset: 31.416;
  }

  50% {
    stroke-dashoffset: 0;
  }

  100% {
    stroke-dashoffset: -31.416;
  }
}

.loading-text {
  @apply text-center space-y-2;
}

.loading-title {
  @apply text-xl font-bold text-gray-900 dark:text-white;
}

.loading-subtitle {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.loading-skeleton {
  @apply space-y-4 w-full max-w-2xl;
}

.skeleton-page {
  @apply h-96 bg-gray-200 dark:bg-neutral-700 rounded-lg animate-pulse;
}

// ===== CHAPTER IMAGES =====
.chapter-page-container {
  @apply block relative object-contain mx-auto;
}

.chapter-page-image {
  @apply object-cover w-full h-auto;

  &.chapter-page-horizontal {
    @apply min-w-80 h-full;
  }

  &.night-mode {
    filter: brightness(0.9) sepia(0.4);    
  }
}

// ===== END CHAPTER NAVIGATION =====
.end-chapter-navigation {
  @apply flex justify-center w-full mt-8 mb-6;
}

.end-chapter-content {
  @apply flex items-center justify-between w-full max-w-4xl gap-6 p-6 bg-white dark:bg-neutral-800 rounded-xl border border-gray-200 dark:border-neutral-700 shadow-sm;
}

.end-nav-button {
  @apply flex items-center gap-3 px-4 py-3 text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-gray-100 dark:bg-neutral-700 hover:bg-primary-100 rounded-lg transition-all duration-200 border border-gray-200 dark:border-neutral-600 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed;

  &.end-nav-active {
    @apply text-white bg-primary-100 border-primary-100;
  }
}

.end-nav-icon {
  @apply w-5 h-5;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.end-nav-text {
  @apply font-medium;
}

.end-chapter-info {
  @apply text-center space-y-2 flex-1 hidden sm:block;
}

.end-chapter-title {
  @apply text-lg font-bold text-gray-900 dark:text-white;
}

.end-chapter-subtitle {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

// ===== SCROLL TO TOP BUTTON =====
.scroll-to-top-btn {
  @apply fixed bottom-6 right-6 z-40 flex items-center gap-2 px-4 py-3 bg-primary-100 hover:bg-primary-200 text-white rounded-full shadow-lg transition-all duration-300 opacity-0 translate-y-4 pointer-events-none;

  &.scroll-to-top-visible {
    @apply opacity-100 translate-y-0 pointer-events-auto;
  }
}

.scroll-to-top-icon {
  @apply w-5 h-5;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.scroll-to-top-text {
  @apply font-medium hidden sm:inline;
}

// ===== RESPONSIVE DESIGN =====
@media (max-width: 1024px) {
  .reading-content {
    @apply mx-0 lg:mx-6;
  }

  .settings-menu-content {
    @apply grid-cols-1 sm:grid-cols-2 gap-3;
  }
}

@media (max-width: 768px) {

  .chapter-navigation-group {
    @apply px-1 gap-0;
  }

  .nav-button {
    @apply px-2 py-1.5 text-xs;
  }

  .end-nav-button {
    @apply w-full justify-center;
  }

  .scroll-to-top-btn {
    @apply bottom-4 right-4 px-3 py-2;
  }
}

@media (max-width: 640px) {
  .chapter-header-card {
    @apply p-3 rounded-t-lg;
  }

  .comic-title-link {
    @apply text-lg;
  }

  .chapter-title {
    @apply text-sm;
  }

  .server-list {
    @apply gap-2;
  }

  .server-button {
    @apply px-3 py-1.5 text-xs;
  }

  .settings-menu-content {
    @apply grid-cols-1 gap-2;
  }

  .settings-btn {
    @apply px-3 py-2 text-xs;
  }


  .scroll-btn-text,
  .end-nav-text,
  .scroll-to-top-text {
    @apply hidden;
  }
}

@media (max-width: 480px) {
  .chapter-info-section {
    @apply space-y-3;
  }

  .report-error-button {
    @apply px-3 py-1.5 gap-1;
  }

  .report-icon {
    @apply w-5 h-5;
  }

  .report-text {
    @apply text-xs;
  }
}


// ===== CONTROL BAR ANIMATIONS =====
@keyframes slideDown {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideUp {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateY(-100%);
    opacity: 0;
  }
}



.translate-bottom {
  @apply -translate-y-full;
}

.translate-top {
  @apply translate-y-full;
}

#image-container {
  // scroll-snap-align: start;
  // flex: 0 0 auto;
  // display: flex;
  // flex-direction: column;
  // overflow-y: auto;
}

// ===== CHAPTER NAVIGATION SPECIFIC =====
.chapter-navigation {
  @apply bg-gray-300 flex justify-center items-center rounded-lg cursor-pointer text-white;
}