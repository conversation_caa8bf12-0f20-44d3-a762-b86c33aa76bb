import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, Input } from '@angular/core';
import { ClickOutsideDirective } from '@directives/click-outside.directive';
import { IServiceResponse, IUser } from '@schema';
import { AccountService } from '@services/account.service';
import { LevelService } from '@services/level.service';
import { timer } from 'rxjs';
import { IPopupComponent } from 'src/app/core/interface';

@Component({
  selector: 'app-user-info-popup',
  templateUrl: './user-info-popup.component.html',
  styleUrl: './user-info-popup.component.scss',
  standalone: true,
  imports: [CommonModule, ClickOutsideDirective],
})
export class UserInfoPopupComponent implements IPopupComponent {
  isLoadInfoUser = false
  @Input() userInfo?: IUser
  isVisible = false
  constructor(private levelService: LevelService,
    private accountService: AccountService,
    private cd: ChangeDetectorRef
  ) { }
  setVisible(isVisible: boolean): void {
    this.isVisible = isVisible;
    this.cd.detectChanges();
  }

  getUserName() {
    return this.userInfo?.firstName + ' ' + (this.userInfo?.lastName ?? '');
  }

  public showUserInfo(userID: number) {
    this.setVisible(true);
    this.accountService.GetUserById(userID).subscribe((res: IServiceResponse<IUser>) => {
      this.userInfo = res.data;
      this.userInfo!.levelInfo = this.levelService.getLevelUser(
        this.userInfo!.experience!,
        this.userInfo!.typeLevel!,
      );

      this.isLoadInfoUser = false;
      this.setVisible(true);
      timer(500).subscribe(() => {
        this.isLoadInfoUser = true;
        this.cd.detectChanges();

      })
    })
  }
  show(o: any) {
    const { userID } = o;
    this.showUserInfo(userID)
    return new Promise((resolve) => { });
  }
}
