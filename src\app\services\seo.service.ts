import { DOCUMENT, isPlatformBrowser } from '@angular/common';
import { Inject, Injectable, PLATFORM_ID } from '@angular/core';
import { Meta, MetaDefinition, Title } from '@angular/platform-browser';

export interface SEOConfig {
  title: string;
  description: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'book' | 'profile';
  siteName?: string;
  locale?: string;
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  section?: string;
  tags?: string[];
}

@Injectable({
  providedIn: 'root',
})
export class SeoService {
  private readonly defaultSiteName = 'MeTruyenMoi';
  private readonly defaultLocale = 'vi_VN';
  private readonly defaultImage = '/favicon.png';

  constructor(
    private title: Title,
    private meta: Meta,
    @Inject(DOCUMENT) private document: Document,
    @Inject(PLATFORM_ID) private platformId: object
  ) { }

  setTitle(title: string) {
    this.title.setTitle(title);
  }

  addTags(tags: MetaDefinition[]) {
    this.meta.addTags(tags);
  }

  updateTag(tag: MetaDefinition) {
    this.meta.updateTag(tag);
  }

  updateLink(ref: string, href: string) {
    const head = this.document.head;
    let element: HTMLLinkElement | null = this.document.querySelector(
      `link[rel='${ref}']`
    );
    if (element == null) {
      element = this.document.createElement('link') as HTMLLinkElement;
      head.appendChild(element);
    }
    element.setAttribute('rel', ref);
    element.setAttribute('href', href);
  }

  /**
   * Comprehensive SEO setup with all meta tags
   */
  setupSEO(config: SEOConfig) {
    // Set title
    this.setTitle(config.title);

    // Clear existing meta tags to avoid duplicates
    this.clearMetaTags();

    // Basic meta tags
    const metaTags: MetaDefinition[] = [
      { name: 'description', content: config.description },
      { name: 'robots', content: 'index, follow' },
      { name: 'viewport', content: 'width=device-width, initial-scale=1' },
    ];

    // Keywords
    if (config.keywords) {
      metaTags.push({ name: 'keywords', content: config.keywords });
    }

    // Author
    if (config.author) {
      metaTags.push({ name: 'author', content: config.author });
    }

    // Open Graph tags
    metaTags.push(
      { property: 'og:title', content: config.title },
      { property: 'og:description', content: config.description },
      { property: 'og:type', content: config.type || 'website' },
      { property: 'og:site_name', content: config.siteName || this.defaultSiteName },
      { property: 'og:locale', content: config.locale || this.defaultLocale }
    );

    if (config.url) {
      metaTags.push({ property: 'og:url', content: config.url });
    }

    if (config.image) {
      metaTags.push({ property: 'og:image', content: config.image });
      metaTags.push({ property: 'og:image:alt', content: config.title });
    }

    // Article specific tags
    if (config.type === 'article') {
      if (config.publishedTime) {
        metaTags.push({ property: 'article:published_time', content: config.publishedTime });
      }
      if (config.modifiedTime) {
        metaTags.push({ property: 'article:modified_time', content: config.modifiedTime });
      }
      if (config.section) {
        metaTags.push({ property: 'article:section', content: config.section });
      }
      if (config.tags) {
        config.tags.forEach(tag => {
          metaTags.push({ property: 'article:tag', content: tag });
        });
      }
    }

    // Twitter Card tags
    metaTags.push(
      { name: 'twitter:card', content: 'summary_large_image' },
      { name: 'twitter:title', content: config.title },
      { name: 'twitter:description', content: config.description }
    );

    if (config.image) {
      metaTags.push({ name: 'twitter:image', content: config.image });
    }

    // Schema.org microdata
    metaTags.push(
      { itemprop: 'name', content: config.title },
      { itemprop: 'description', content: config.description }
    );

    if (config.image) {
      metaTags.push({ itemprop: 'image', content: config.image });
    }

    // Add all meta tags
    this.addTags(metaTags);

    // Set canonical URL
    if (config.url) {
      this.updateLink('canonical', config.url);
    }
  }

  /**
   * Add structured data (JSON-LD) to the page
   */
  addStructuredData(data: any) {
    if (!isPlatformBrowser(this.platformId)) return;

    // Remove existing structured data
    this.removeStructuredData();

    const script = this.document.createElement('script');
    script.type = 'application/ld+json';
    script.text = JSON.stringify(data);
    script.id = 'structured-data';
    this.document.head.appendChild(script);
  }

  /**
   * Remove existing structured data
   */
  private removeStructuredData() {
    const existingScript = this.document.getElementById('structured-data');
    if (existingScript) {
      existingScript.remove();
    }
  }

  /**
   * Clear existing meta tags to avoid duplicates
   */
  private clearMetaTags() {
    // Remove existing meta tags that we manage
    const tagsToRemove = [
      'description', 'keywords', 'author', 'robots',
      'og:title', 'og:description', 'og:type', 'og:url', 'og:image', 'og:site_name', 'og:locale',
      'twitter:card', 'twitter:title', 'twitter:description', 'twitter:image',
      'article:published_time', 'article:modified_time', 'article:section', 'article:tag'
    ];

    tagsToRemove.forEach(tag => {
      if (tag.startsWith('og:') || tag.startsWith('article:')) {
        this.meta.removeTag(`property="${tag}"`);
      } else if (tag.startsWith('twitter:')) {
        this.meta.removeTag(`name="${tag}"`);
      } else {
        this.meta.removeTag(`name="${tag}"`);
      }
    });
  }

  /**
   * Generate breadcrumb structured data
   */
  generateBreadcrumbSchema(breadcrumbs: Array<{name: string, url: string}>, baseUrl: string) {
    const breadcrumbList = {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      "itemListElement": breadcrumbs.map((crumb, index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "name": crumb.name,
        "item": crumb.url.startsWith('http') ? crumb.url : `${baseUrl}${crumb.url}`
      }))
    };

    this.addStructuredData(breadcrumbList);
  }

  /**
   * Generate website schema
   */
  generateWebsiteSchema(baseUrl: string, siteName: string) {
    const websiteSchema = {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": siteName,
      "url": baseUrl,
      "potentialAction": {
        "@type": "SearchAction",
        "target": {
          "@type": "EntryPoint",
          "urlTemplate": `${baseUrl}/tim-truyen?q={search_term_string}`
        },
        "query-input": "required name=search_term_string"
      }
    };

    this.addStructuredData(websiteSchema);
  }

  /**
   * Generate organization schema
   */
  generateOrganizationSchema(baseUrl: string, siteName: string) {
    const organizationSchema = {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": siteName,
      "url": baseUrl,
      "logo": `${baseUrl}/favicon.png`,
      "sameAs": [
        // Add social media URLs here
      ]
    };

    this.addStructuredData(organizationSchema);
  }

  /**
   * Generate comic book schema for comic detail pages
   */
  generateComicSchema(comic: any, baseUrl: string) {
    const comicSchema = {
      "@context": "https://schema.org",
      "@type": "Book",
      "name": comic.title,
      "description": comic.description,
      "image": comic.coverImage,
      "url": `${baseUrl}/truyen-tranh/${comic.url}-${comic.id}`,
      "author": {
        "@type": "Person",
        "name": comic.author || "Unknown"
      },
      "genre": comic.genres?.map((g: any) => g.name).join(", "),
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": comic.rating,
        "ratingCount": comic.viewCount,
        "bestRating": 10,
        "worstRating": 0
      },
      "dateModified": comic.updateAt,
      "inLanguage": "vi"
    };

    this.addStructuredData(comicSchema);
  }
}
