import { ChangeDetectorRef, Component, computed, Inject, OnInit, PLATFORM_ID, signal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { ChatBoxBubbleComponent } from '@components/lazy/chat-box/chat-box-bubble/chat-box-bubble.component';
import { SeoService } from '@services/seo.service';
import { UrlService } from '@services/url.service';

@Component({
  selector: 'app-ai-assistant',
  templateUrl: './ai-assistant.component.html',
  styleUrls: ['./ai-assistant.component.scss'],
  standalone: false
})
export class AiAssistantComponent extends OptimizedBaseComponent implements OnInit {

  // Signals for reactive state management
  private readonly isLoadingSignal = signal<boolean>(false);
  private readonly chatBoxVisibleSignal = signal<boolean>(false);

  // Computed properties
  readonly isLoading = computed(() => this.isLoadingSignal());
  readonly chatBoxVisible = computed(() => this.chatBoxVisibleSignal());

  // Anime character images (fallback to placeholder if not found)
  readonly characterImages = {
    aiAssistant: '/option4.png',
   
  };

  constructor(
    private route: ActivatedRoute,

    private seoService: SeoService,
    private urlService: UrlService,
    override  cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) override platformId: object,
  ) {
    super(cd, platformId);
  }

  ngOnInit(): void {
    this.route.data.subscribe((data) => {
      this.setupSEO();
    });
    this.route.params.subscribe((params) => {
      this.setupSEO();
    });
  }

  /**
   * Open AI Chatbox
   */
  openChatBox(): void {
    this.runInBrowser(() => {
      // Show the chat box bubble component
      if (ChatBoxBubbleComponent.Instance) {
        // Hide the bubble and show the chat box
        ChatBoxBubbleComponent.Instance.showChat();
        this.chatBoxVisibleSignal.set(true);
      } else {
        // If no instance available, show a message
        console.warn('ChatBox instance not available. Please ensure the chat-box-bubble component is loaded.');
      }
    });
  }

  /**
   * Handle image load error - fallback to placeholder
   */
  onImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    if (img) {
      // Use a generic anime placeholder or default avatar
      img.src = 'https://jbagy.me/wp-content/uploads/2025/03/Hinh-anh-anime-dang-yeu-khong-the-cuong-duoc-2.jpg';
      img.onerror = null; // Prevent infinite loop
    }
  }

  /**
   * Get character image with fallback
   */
  getCharacterImage(characterKey: keyof typeof this.characterImages): string {
    return this.characterImages[characterKey] || '/default-anime-avatar.png';
  }

  /**
   * Setup SEO for AI Assistant page
   */
  private setupSEO(): void {
    const title = 'Trợ lý AI - Chatbot thông minh cho truyện tranh | MeTruyenMoi';
    const description = 'Trợ lý AI thông minh giúp bạn tìm kiếm, gợi ý và trò chuyện về truyện tranh. Khám phá tính năng chatbot AI tiên tiến tại MeTruyenMoi.';
    const url = `${this.urlService.baseUrl}/tro-ly-ai`;

    const seoData = {
      title,
      description,
      type: 'website' as const,
      url,
      image: '/logo.png',
      siteName: 'MeTruyenMoi',
      canonical: url,
      twitterCard: 'summary_large_image' as const,
      keywords: 'trợ lý AI, chatbot, truyện tranh, AI assistant, comic chatbot, manga AI'
    };

    this.seoService.setSEOData(seoData);

    // Add structured data for AI Assistant page
    const pageSchema = {
      '@context': 'https://schema.org',
      '@type': 'WebPage',
      name: 'Trợ lý AI',
      description: description,
      url: url,
      mainEntity: {
        '@type': 'SoftwareApplication',
        name: 'Trợ lý AI MeTruyenMoi',
        description: 'Chatbot AI thông minh cho truyện tranh',
        applicationCategory: 'ChatApplication',
        operatingSystem: 'Web Browser'
      }
    };

    this.seoService.addStructuredData([pageSchema]);
  }


}
