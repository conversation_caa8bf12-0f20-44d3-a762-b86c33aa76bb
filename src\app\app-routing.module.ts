import { NgModule } from '@angular/core';
import { PreloadAllModules, RouterModule, Routes } from '@angular/router';
import { LayoutComponent } from '@components/layout/layout.component';

const routes: Routes = [
  {
    path: '',
    component: LayoutComponent,
    children: [
      {
        path: '',
        loadChildren: () => import('./modules/home/<USER>').then((m) => m.HomeModule),
        data: {
          seo: {
            title: 'MeTruyenMoi - Đọc truyện tranh Manhwa, Manga, Manhua nhanh nhất',
            description: 'MeTruyenMoi - Đọc truyện tranh online miễn phí, được cập nhật mỗi ngày. Với các thể loại từ hành động, ngôn tình, phi<PERSON>u lưu, hài hước, lãng mạn và nhiều hơn nữa.',
            keywords: 'truyen tranh, truyện tranh, mê truyện mới, truyen tranh online, đọc truyện tranh, truyện tranh hot, truyện tranh hay, manga, manhua, manhwa',
            type: 'website'
          }
        }
      },
      {
        path: 'truyen-tranh/:id',
        loadChildren: () => import('./modules/comic-detail/comic-detail.module').then((m) => m.DetailModule,),
        data: {
          seo: {
            type: 'book',
            section: 'Comics'
          }
        }
      },
      {
        path: 'tim-truyen',
        loadChildren: () => import('./modules/search-page/search.module').then((m) => m.SearchModule,),
        data: {
          seo: {
            title: 'Tìm kiếm truyện tranh - MeTruyenMoi',
            description: 'Tìm kiếm và khám phá hàng ngàn bộ truyện tranh hay nhất. Lọc theo thể loại, tác giả, trạng thái và nhiều tiêu chí khác.',
            keywords: 'tìm kiếm truyện tranh, search manga, lọc truyện, thể loại truyện tranh',
            type: 'website'
          }
        }
      },
      {
        path: 'xep-hang',
        loadChildren: () => import('./modules/rank/rank.module').then((m) => m.RankModule),
        data: {
          seo: {
            title: 'Bảng xếp hạng truyện tranh - MeTruyenMoi',
            description: 'Bảng xếp hạng các bộ truyện tranh được yêu thích nhất, đọc nhiều nhất và đánh giá cao nhất tại MeTruyenMoi.',
            keywords: 'bảng xếp hạng truyện tranh, top manga, truyện tranh hot nhất, truyện tranh hay nhất',
            type: 'website'
          }
        }
      },
      {
        path: 'lich-su',
        loadChildren: () => import('./modules/history-page/history-page.module').then((m) => m.HistoryPageModule),
        data: {
          seo: {
            title: 'Lịch sử đọc truyện - MeTruyenMoi',
            description: 'Xem lại lịch sử các bộ truyện tranh bạn đã đọc. Tiếp tục đọc từ chương cuối cùng một cách dễ dàng.',
            keywords: 'lịch sử đọc truyện, history manga, tiếp tục đọc truyện',
            type: 'website'
          }
        }
      },
      {
        path: 'theo-doi',
        loadChildren: () => import('./modules/followed-page/followed-page.module').then((m) => m.FollowedPageModule,),
        data: {
          seo: {
            title: 'Truyện đã theo dõi - MeTruyenMoi',
            description: 'Danh sách các bộ truyện tranh bạn đang theo dõi. Nhận thông báo khi có chương mới cập nhật.',
            keywords: 'theo dõi truyện tranh, follow manga, bookmark truyện, yêu thích',
            type: 'website'
          }
        }
      },
      {
        path: 'truyen-hot',
        loadChildren: () => import('./modules/comic-hot/comic-hot.module').then((m) => m.ComicHotModule,),
        data: {
          seo: {
            title: 'Truyện tranh hot - Xu hướng đọc truyện mới nhất',
            description: 'Khám phá những bộ truyện tranh đang hot nhất, được cộng đồng yêu thích và thảo luận nhiều nhất.',
            keywords: 'truyện tranh hot, manga trending, truyện tranh xu hướng, truyện tranh viral',
            type: 'website'
          }
        }
      },
      {
        path: 'truyen-tranh/:comicid/:chapterid',
        loadChildren: () => import('./modules/chapter-page/chapter.module').then((m) => m.ChapterModule,),
        data: {
          seo: {
            type: 'article',
            section: 'Chapter'
          }
        }
      },
      {
        path: 'tai-khoan',
        loadChildren: () => import('./modules/user/user.module').then((m) => m.UserModule),
        data: {
          seo: {
            title: 'Tài khoản cá nhân - MeTruyenMoi',
            description: 'Quản lý tài khoản, xem thống kê đọc truyện, cập nhật thông tin cá nhân và cài đặt tùy chọn.',
            keywords: 'tài khoản, profile, thống kê đọc truyện, cài đặt',
            type: 'profile'
          }
        }
      },
      {
        path: 'auth',
        loadChildren: () => import('./modules/authentication/auth.module').then((m) => m.AuthModule,),
        data: {
          seo: {
            title: 'Đăng nhập / Đăng ký - MeTruyenMoi',
            description: 'Đăng nhập hoặc tạo tài khoản mới để trải nghiệm đầy đủ các tính năng của MeTruyenMoi.',
            keywords: 'đăng nhập, đăng ký, login, register, tạo tài khoản',
            type: 'website'
          }
        }
      },
      {
        path: 'chinh-sach-bao-mat',
        loadChildren: () => import('./modules/privacy-policy/privacy-policy.module').then((m) => m.PrivacyPolicyModule),
        data: {
          seo: {
            title: 'Chính sách bảo mật - MeTruyenMoi',
            description: 'Chính sách bảo mật thông tin cá nhân và quyền riêng tư của người dùng tại MeTruyenMoi.',
            keywords: 'chính sách bảo mật, privacy policy, bảo vệ thông tin cá nhân, GDPR',
            type: 'website'
          }
        }
      },
      {
        path: 'dieu-khoan',
        loadChildren: () => import('./modules/clause/clause.module').then((m) => m.ClauseModule),
        data: {
          seo: {
            title: 'Điều khoản sử dụng - MeTruyenMoi',
            description: 'Điều khoản và điều kiện sử dụng dịch vụ đọc truyện tranh tại MeTruyenMoi.',
            keywords: 'điều khoản sử dụng, terms of service, quy định sử dụng',
            type: 'website'
          }
        }
      },
      {
        path: 'lien-he',
        loadChildren: () => import('./modules/contact/contact.module').then((m) => m.ContactModule),
        data: {
          seo: {
            title: 'Liên hệ - Hỗ trợ khách hàng MeTruyenMoi',
            description: 'Liên hệ với đội ngũ hỗ trợ MeTruyenMoi. Chúng tôi sẵn sàng giải đáp mọi thắc mắc và hỗ trợ bạn 24/7.',
            keywords: 'liên hệ, contact, hỗ trợ khách hàng, customer support, báo cáo lỗi',
            type: 'website'
          }
        }
      },
      {
        path: 'dong-bo-truyen',
        loadChildren: () => import('./modules/sync-tracking/sync-tracking.module').then((m) => m.SyncTrackingModule),
        data: {
          seo: {
            title: 'Đồng bộ tiến độ đọc truyện - MeTruyenMoi',
            description: 'Đồng bộ tiến độ đọc truyện giữa các thiết bị. Không bao giờ mất dấu chương đang đọc.',
            keywords: 'đồng bộ truyện, sync reading progress, backup reading history',
            type: 'website'
          }
        }
      },
      {
        path: '**',
        redirectTo: '404',
        pathMatch: 'full'
      },
      {
        path: '404',
        loadChildren: () => import('./modules/not-found/not-found.module').then((m) => m.NotFoundModule),
        data: {
          seo: {
            title: '404 - Trang không tồn tại | MeTruyenMoi',
            description: 'Trang bạn đang tìm kiếm không tồn tại. Khám phá những bộ truyện tranh hay nhất tại MeTruyenMoi.',
            keywords: '404, trang không tồn tại, page not found',
            type: 'website'
          }
        }
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forRoot(routes, {
    // Enable router preloading for better performance
    preloadingStrategy: PreloadAllModules,
    // Enable tracing for debugging (disable in production)
    enableTracing: false,
    // Scroll to top on route change
    scrollPositionRestoration: 'top',
    // Use hash for fragment navigation
    anchorScrolling: 'enabled',
    // Initial navigation
    initialNavigation: 'enabledBlocking'
  })],
  exports: [RouterModule],
})
export class AppRoutingModule { }
